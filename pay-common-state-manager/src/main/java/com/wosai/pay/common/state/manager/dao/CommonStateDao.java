package com.wosai.pay.common.state.manager.dao;

import com.wosai.pay.common.data.Jackson2PersistenceHelper;
import com.wosai.pay.common.data.jdbc.JdbcVersionedRecordDao;


import com.wosai.pay.common.state.manager.constant.StateManagerConstant;
import com.wosai.pay.common.state.manager.entity.CommonStateEntity;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

public class CommonStateDao extends JdbcVersionedRecordDao<Long, CommonStateEntity> {

    private static final String TABLE_NAME = StateManagerConstant.TABLE_STATE;

    public CommonStateDao(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        super(TABLE_NAME, CommonStateEntity.class, "", namedParameterJdbcTemplate, new Jackson2PersistenceHelper());
    }
}

