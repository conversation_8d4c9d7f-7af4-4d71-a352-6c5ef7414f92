package com.wosai.pay.common.state.manager.service;

import com.wosai.pay.common.data.Criteria;
import com.wosai.pay.common.state.manager.constant.StateManagerConstant;
import com.wosai.pay.common.state.manager.dao.CommonStateDao;
import com.wosai.pay.common.state.manager.request.AbstractEntity;
import com.wosai.pay.common.state.manager.request.StateManagerRequest;
import com.wosai.pay.common.state.manager.entity.CommonStateEntity;
import com.wosai.pay.common.state.manager.request.OperationLogRequest;
import com.wosai.pay.common.state.manager.exception.StateManageBizException;
import com.wosai.pay.common.state.manager.result.StateConfigurationResult;
import com.wosai.pay.common.state.manager.result.StateManagerResult;
import com.wosai.pay.common.state.manager.service.processor.StateManagerProcessor;
import com.wosai.pay.common.state.manager.service.processor.StateManagerProcessorRegistry;
import org.checkerframework.checker.units.qual.A;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * 状态服务实现类
 * 实现了状态的查询和更新逻辑，支持不同业务类型的自定义处理
 */
public class StateManagerServiceImpl implements StateManagerService {

    private static final Logger LOGGER = Logger.getLogger(StateManagerServiceImpl.class.getName());

    private final CommonStateDao commonStateDao;

    public StateManagerServiceImpl(CommonStateDao commonStateDao) {
        this.commonStateDao = commonStateDao;
    }

    /**
     * 注册一个状态处理器
     *
     * @param processor 要注册的处理器
     * @return 注册是否成功
     */
    public boolean registerProcessor(StateManagerProcessor<?, ?> processor) {
        return StateManagerProcessorRegistry.register(processor);
    }


    @Override
    @Transactional(isolation = Isolation.REPEATABLE_READ)
    public <R extends StateManagerResult> Boolean updateState(StateManagerRequest<AbstractEntity> stateManagerRequest, OperationLogRequest operationLogRequest) {
        // 验证业务类型和状态类型
        String businessType = stateManagerRequest.getBusiness();
        Integer subStateType = stateManagerRequest.getSubStateType();
        StateManagerConfig.validBizAndType(businessType, subStateType);

        //查询变更前状态
        R oldStateResult = queryState(stateManagerRequest);

        // 获取实体类型对应的处理器
        String entityType = stateManagerRequest.getEntityType();
        StateManagerProcessor<AbstractEntity, StateManagerResult> processor = StateManagerProcessorRegistry.getProcessor(entityType);
        if (processor == null) {
            throw new IllegalStateException("未找到实体类型[" + entityType + "]对应的处理器");
        }

        // 初始化或获取状态实体
        CommonStateEntity entity = initAndGetStateByBizIdAndEntity(stateManagerRequest.getBusiness(),stateManagerRequest.getEntity(), processor);
        String originalSubState = entity.getSubStatesBits();
        // 更新子状态
        Map<Integer, Boolean> subStates = entity.getSubStates();
        if (subStates == null) {
            subStates = new HashMap<>();
            entity.setSubStates(subStates);
        }
        subStates.put(subStateType, stateManagerRequest.getEnabled());
        entity.setSubStates(subStates);
        // 更新总状态和其他信息
        entity.setRemark(stateManagerRequest.getRemark());
        entity.setState(StateManagerConstant.ALL_ENABLED_STATE_BITS.equals(entity.getSubStatesBits()));
        if (originalSubState!=null && originalSubState.equals(entity.getSubStatesBits())) {
            return Boolean.TRUE;
        }
        // 更新状态
        commonStateDao.update(entity);

        //查询变更后状态
        R curStateResult = queryState(stateManagerRequest);

        // 后置处理
        processor.afterStateUpdate(stateManagerRequest, oldStateResult, curStateResult, operationLogRequest);

        return Boolean.TRUE;
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T extends StateManagerRequest, R extends StateManagerResult> R queryState(StateManagerRequest<AbstractEntity> stateManagerRequest) {
        String business = stateManagerRequest.getBusiness();
        String entityType = stateManagerRequest.getEntityType();
        StateManagerConfig.validBizAndType(business, null);
        List<StateConfigurationResult.SubStateConfiguration> stateConfigurationList = StateManagerConfig.getStatesList(business);

        // 获取业务类型对应的处理器
        StateManagerProcessor<AbstractEntity, StateManagerResult> processor = StateManagerProcessorRegistry.getProcessor(entityType);
        if (processor == null) {
            throw new IllegalStateException("未找到实体类型[" + entityType + "]对应的处理器");
        }
        CommonStateEntity entity = initAndGetStateByBizIdAndEntity(business,stateManagerRequest.getEntity(), processor);

        // 创建返回结果
        R result = (R) processor.createResultInstance(stateManagerRequest);

        // 设置基本状态信息
        result.setState(entity.getState());

        // 设置子状态列表
        List<StateManagerResult.SubState> subStateList = new ArrayList<>();
        Map<Integer, Boolean> subStates = entity.getSubStates();

        if (stateConfigurationList != null) {
            for (StateConfigurationResult.SubStateConfiguration stateConfiguration : stateConfigurationList) {
                StateManagerResult.SubState subState = new StateManagerResult.SubState();
                Integer subStateType = stateConfiguration.getSubStateType();
                subState.setType(subStateType);
                subState.setDesc(stateConfiguration.getDescription());
                Boolean value = subStates.get(subStateType);
                subState.setValue(value != null ? value : true );
                subStateList.add(subState);
            }
        }

        result.setSubStateList(subStateList);

        return result;
    }

    private <T extends AbstractEntity, E extends CommonStateEntity> CommonStateEntity initAndGetStateByBizIdAndEntity(String business,T abstractEntity, StateManagerProcessor processor) {
        CommonStateEntity entity;
        Criteria criteria = processor.generateCriteria(abstractEntity);
        if (criteria == null) {
            throw new StateManageBizException("generateCriteria return is null");
        }
        criteria.with(StateManagerConstant.BUSINESS).is(business);
        entity = commonStateDao.filter(criteria).fetchOne();
        if (entity == null) {
            try {
                E newEntity = (E) processor.buildCommonStateEntity(abstractEntity);
                newEntity.setBusiness(business);
                newEntity.setState(Boolean.TRUE);
                newEntity.setSubStates(null);
                commonStateDao.insert(newEntity);
            } catch (DuplicateKeyException e) {
                LOGGER.info("DuplicateKeyException");
            }
            entity = commonStateDao.filter(criteria).fetchOne();
        }
        return entity;
    }
}
