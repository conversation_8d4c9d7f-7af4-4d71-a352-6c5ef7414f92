package com.wosai.pay.common.state.manager.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import org.reflections.Reflections;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.lang.reflect.Modifier;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;


@Data
public abstract class AbstractEntity {

    public static final Logger logger = LoggerFactory.getLogger(AbstractEntity.class);

    private static final Map<String, Class<? extends AbstractEntity>> typeId2Class = new HashMap<>();
    private static volatile boolean initialized = false;

    public static void registerType(String objectType, Class<? extends AbstractEntity> clazz) {
        typeId2Class.put(String.format("%s", objectType), clazz);
    }

    // 添加自定义反序列化器
    public static class AbstractEntityDeserializer
            extends JsonDeserializer<AbstractEntity> {

        @Override
        public AbstractEntity deserialize(
                JsonParser parser,
                DeserializationContext context
        ) throws IOException {

            // 解析为JSON树节点
            JsonNode node = parser.getCodec().readTree(parser);

            // 验证必须包含objectType字段
            if (!node.has("objectType")) {
                throw new IllegalStateException(
                        "JSON数据缺少必需的objectType字段");
            }

            String objectType = node.get("objectType").asText();

            // 获取目标类
            Class<? extends AbstractEntity> targetClass = AbstractEntity.classFor(objectType);
            if (targetClass == null) {
                throw new IllegalArgumentException(
                        "未知的对象类型: " + objectType);
            }

            // 转换为具体类型
            return parser.getCodec().treeToValue(node, targetClass);
        }
    }

    public static Class<? extends AbstractEntity> classFor(String objectType) {
        if (!initialized) {
            synchronized (AbstractEntity.class) {
                if (!initialized) {
                    initialize();
                    initialized = true;
                }
            }
        }
        return typeId2Class.get(String.format("%s", objectType));
    }

    private static void initialize() {
        Reflections reflections = new Reflections(AbstractEntity.class.getPackage().getName());
        Set<Class<? extends AbstractEntity>> subTypes = reflections.getSubTypesOf(AbstractEntity.class);
        for (Class<? extends AbstractEntity> type : subTypes) {
            if (!Modifier.isAbstract(type.getModifiers())) {
                try {
                    type.newInstance();
                } catch (InstantiationException | IllegalAccessException e) {
                    logger.error("failed to load event type {}", type.getName(), e);
                }
            }
        }
    }



    private String objectType;

    @JsonProperty("business")
    private String business;

    /**
     * 总状态值
     */
    @JsonProperty("state")
    private Boolean state;

    /**
     * 子状态位串，每个位表示一个子状态
     * 1表示正常，0表示关闭
     */
    @JsonProperty("sub_states_bits")
    private String subStatesBits;

    /**
     * 备注
     */
    @JsonProperty("remark")
    private String remark;

    /**
     * 额外字段
     */
    @JsonProperty("extra")
    private Map<String, Object> extra;


    public String getObjectType() {
        return objectType;
    }

    public void setObjectType(String objectType) {
        this.objectType = objectType;
    }
}
