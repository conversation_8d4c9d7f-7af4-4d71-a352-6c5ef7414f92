package com.wosai.pay.common.state.manager.service;

import com.wosai.pay.common.state.manager.request.AbstractEntity;
import com.wosai.pay.common.state.manager.request.StateManagerRequest;
import com.wosai.pay.common.state.manager.request.OperationLogRequest;
import com.wosai.pay.common.state.manager.result.StateManagerResult;
import com.wosai.pay.common.state.manager.service.processor.StateManagerProcessor;

public interface StateManagerService {

    /**
     * 更新业务的值（记录日志）
     *
     * @param stateManagerRequest                StateManagerBaseDTO 的子类类型
     * @param operationLogRequest  更新状态请求，必须是 StateManagerBaseDTO 的子类
     * @param operationLogRequest 操作日志请求
     * @return 是否操作成功
     */
    <R extends StateManagerResult> Boolean updateState(StateManagerRequest<AbstractEntity> stateManagerRequest, OperationLogRequest operationLogRequest);

    /**
     * 根据业务查询 状态值
     *
     * @param <T>                 stateManagerBaseDTO 的子类类型
     * @param <R>                 StateManagerResult 的子类类型
     * @param stateManagerRequest 查询状态请求，必须是 stateManagerBaseDTO 的子类
     * @return 业务详情，返回 TradeStateResult 的子类
     */
    <T extends StateManagerRequest, R extends StateManagerResult> R queryState(StateManagerRequest<AbstractEntity> stateManagerRequest);

    /**
     * 注册一个状态处理器
     *
     * @param processor 要注册的处理器
     * @return 注册是否成功
     */
    boolean registerProcessor(StateManagerProcessor<?, ?> processor);

}
