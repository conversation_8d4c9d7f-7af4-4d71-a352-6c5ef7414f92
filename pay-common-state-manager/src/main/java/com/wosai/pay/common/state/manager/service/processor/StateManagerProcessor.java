package com.wosai.pay.common.state.manager.service.processor;

import com.wosai.pay.common.data.Criteria;
import com.wosai.pay.common.state.manager.entity.CommonStateEntity;
import com.wosai.pay.common.state.manager.request.AbstractEntity;
import com.wosai.pay.common.state.manager.request.StateManagerRequest;
import com.wosai.pay.common.state.manager.request.OperationLogRequest;
import com.wosai.pay.common.state.manager.result.StateManagerResult;

/**
 * 状态处理器接口
 * 不同类型可以实现此接口来自定义状态处理逻辑
 */
public interface StateManagerProcessor<T extends AbstractEntity, R extends StateManagerResult> {

    /**
     * 获取此处理器支持的实体类型
     * @return 业务类型
     */
    String getSupportedEntityType();


    Criteria  generateCriteria( T stateManagerBaseDTO);


    <E extends CommonStateEntity> E buildCommonStateEntity(T stateManagerBaseDTO);


    /**
     * 创建状态查询结果实例
     * @param StateManagerRequest 状态查询请求
     * @return 状态查询结果
     */
    R createResultInstance(StateManagerRequest stateManagerRequest);

    /**
     * 在状态更新后执行的操作
     * @param stateManagerRequest 状态更新请求
     * @param previousState 修改前状态
     * @param currentState 修改后状态
     */
     void afterStateUpdate(StateManagerRequest<T> stateManagerRequest, R previousState, R currentState, OperationLogRequest operationLogRequest);
}