package com.wosai.pay.common.state.manager.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.pay.common.data.VersionedRecord;

import java.util.HashMap;
import java.util.Map;

import static com.wosai.pay.common.state.manager.constant.StateManagerConstant.ALL_ENABLED_STATE_BITS;


/**
 * 状态实体基类
 * 不同业务类型可以通过继承此类添加自己特定的字段
 */
public class CommonStateEntity extends VersionedRecord<Long> {

    @JsonProperty("business")
    private String business;

    /**
     * 总状态值
     */
    @JsonProperty("state")
    private Boolean state;

    /**
     * 子状态位串，每个位表示一个子状态
     * 1表示正常，0表示关闭
     */
    @JsonProperty("sub_states_bits")
    private String subStatesBits;

    /**
     * 备注
     */
    @JsonProperty("remark")
    private String remark;

    /**
     * 额外字段
     */
    @JsonProperty("extra")
    private Map<String, Object> extra;

    public String getBusiness() {
        return business;
    }

    public void setBusiness(String business) {
        this.business = business;
    }

    public Boolean getState() {
        return state;
    }

    public void setState(Boolean state) {
        this.state = state;
    }

    public String getSubStatesBits() {
        return subStatesBits;
    }

    public void setSubStatesBits(String subStatesBits) {
        this.subStatesBits = subStatesBits;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Map<String, Object> getExtra() {
        return extra;
    }

    public void setExtra(Map<String, Object> extra) {
        this.extra = extra;
    }


    /**
     * 获取子状态Map
     *
     * @return 子状态Map，键为状态类型，值为状态值
     */
    @JsonIgnore
    public Map<Integer, Boolean> getSubStates() {
        Map<Integer, Boolean> subStates = new HashMap<>();
        if (subStatesBits != null && !subStatesBits.isEmpty()) {
            for (int i = 0; i < subStatesBits.length(); i++) {
                if (subStatesBits.charAt(i) == '1') {
                    subStates.put(i + 1, Boolean.TRUE);
                } else {
                    subStates.put(i + 1, Boolean.FALSE);
                }
            }
        }
        return subStates;
    }

    /**
     * 设置子状态Map
     *
     * @param subStates 子状态Map
     */
    public void setSubStates(Map<Integer, Boolean> subStates) {
        if (subStates != null && !subStates.isEmpty()) {
            StringBuilder sb = new StringBuilder(ALL_ENABLED_STATE_BITS);
            for (Map.Entry<Integer, Boolean> entry : subStates.entrySet()) {
                Integer type = entry.getKey();
                Boolean value = entry.getValue();
                if (type != null && value != null) {
                    sb.replace(type - 1, type, value ? "1" : "0");
                }
            }
            this.subStatesBits = sb.toString();
        } else {
            this.subStatesBits = ALL_ENABLED_STATE_BITS;
        }
    }

    public CommonStateEntity() {
    }


}
